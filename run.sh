#!/bin/bash

# Rise Testnet Bot - Auto Install & Run Script
# Secure Crypto Airdrop Hunter

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner
print_banner() {
    echo -e "${GREEN}"
    echo "██████╗░██╗░██████╗███████╗  ████████╗███████╗░██████╗████████╗███╗░░██╗███████╗████████╗"
    echo "██╔══██╗██║██╔════╝██╔════╝  ╚══██╔══╝██╔════╝██╔════╝╚══██╔══╝████╗░██║██╔════╝╚══██╔══╝"
    echo "██████╔╝██║╚█████╗░█████╗░░  ░░░██║░░░█████╗░░╚█████╗░░░░██║░░░██╔██╗██║█████╗░░░░░██║░░░"
    echo "██╔══██╗██║░╚═══██╗██╔══╝░░  ░░░██║░░░██╔══╝░░░╚═══██╗░░░██║░░░██║╚████║██╔══╝░░░░░██║░░░"
    echo "██║░░██║██║██████╔╝███████╗  ░░░██║░░░███████╗██████╔╝░░░██║░░░██║░╚███║███████╗░░░██║░░░"
    echo "╚═╝░░╚═╝╚═╝╚═════╝░╚══════╝  ░░░╚═╝░░░╚══════╝╚═════╝░░░░╚═╝░░░╚═╝░░╚══╝╚══════╝░░░╚═╝░░░"
    echo -e "${NC}"
    echo -e "${CYAN}================================================================================${NC}"
    echo -e "${GREEN}                           RISE TESTNET BOT                                   ${NC}"
    echo -e "${YELLOW}                    Secure Airdrop Hunter & Testnet Bot                      ${NC}"
    echo -e "${CYAN}================================================================================${NC}"
    echo ""
}

# Check if Python 3 is installed
check_python() {
    echo -e "${BLUE}🔍 Checking Python installation...${NC}"
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
        echo -e "${GREEN}✅ Python3 found: $PYTHON_VERSION${NC}"
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
        if [[ $PYTHON_VERSION == 3.* ]]; then
            echo -e "${GREEN}✅ Python3 found: $PYTHON_VERSION${NC}"
            PYTHON_CMD="python"
        else
            echo -e "${RED}❌ Python 3 is required but Python $PYTHON_VERSION found${NC}"
            echo -e "${YELLOW}Please install Python 3.8 or higher${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Python is not installed${NC}"
        echo -e "${YELLOW}Please install Python 3.8 or higher${NC}"
        exit 1
    fi
}

# Check if pip is installed
check_pip() {
    echo -e "${BLUE}🔍 Checking pip installation...${NC}"
    if command -v pip3 &> /dev/null; then
        echo -e "${GREEN}✅ pip3 found${NC}"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        echo -e "${GREEN}✅ pip found${NC}"
        PIP_CMD="pip"
    else
        echo -e "${RED}❌ pip is not installed${NC}"
        echo -e "${YELLOW}Installing pip...${NC}"
        curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
        $PYTHON_CMD get-pip.py
        rm get-pip.py
        PIP_CMD="pip"
    fi
}

# Create virtual environment
setup_venv() {
    echo -e "${BLUE}🔧 Setting up virtual environment...${NC}"
    
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}📦 Creating virtual environment...${NC}"
        $PYTHON_CMD -m venv venv
        echo -e "${GREEN}✅ Virtual environment created${NC}"
    else
        echo -e "${GREEN}✅ Virtual environment already exists${NC}"
    fi
    
    # Activate virtual environment
    echo -e "${YELLOW}🔄 Activating virtual environment...${NC}"
    source venv/bin/activate
    echo -e "${GREEN}✅ Virtual environment activated${NC}"
}

# Install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Check if requirements are already installed
    if [ -f "venv/installed.flag" ]; then
        echo -e "${GREEN}✅ Dependencies already installed${NC}"
        return
    fi
    
    echo -e "${YELLOW}⬇️  Installing Python packages...${NC}"
    
    # Upgrade pip first
    pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        echo -e "${GREEN}✅ All dependencies installed successfully${NC}"
        
        # Create flag file to mark installation complete
        touch venv/installed.flag
    else
        echo -e "${RED}❌ requirements.txt not found${NC}"
        exit 1
    fi
}

# Setup configuration files
setup_config() {
    echo -e "${BLUE}⚙️  Setting up configuration files...${NC}"
    
    # Create pvkey.txt if not exists
    if [ ! -f "pvkey.txt" ]; then
        echo -e "${YELLOW}📝 Creating pvkey.txt template...${NC}"
        cat > pvkey.txt << 'EOF'
# Add your private keys here, one per line
# Example: ******************************************90abcdef1234567890abcdef
# WARNING: Only use testnet wallets, never mainnet wallets!

EOF
        echo -e "${GREEN}✅ pvkey.txt template created${NC}"
        echo -e "${YELLOW}⚠️  Please add your testnet private keys to pvkey.txt${NC}"
    fi
    
    # Create address.txt if not exists
    if [ ! -f "address.txt" ]; then
        echo -e "${YELLOW}📝 Creating address.txt template...${NC}"
        cat > address.txt << 'EOF'
# Add destination addresses here, one per line
# Example: ******************************************

EOF
        echo -e "${GREEN}✅ address.txt template created${NC}"
    fi
    
    # Create addressERC20.txt if not exists
    if [ ! -f "addressERC20.txt" ]; then
        echo -e "${YELLOW}📝 Creating addressERC20.txt template...${NC}"
        cat > addressERC20.txt << 'EOF'
# Add ERC20 destination addresses here, one per line
# Example: ******************************************

EOF
        echo -e "${GREEN}✅ addressERC20.txt template created${NC}"
    fi
    
    # Create mail.txt if not exists
    if [ ! -f "mail.txt" ]; then
        echo -e "${YELLOW}📝 Creating mail.txt template...${NC}"
        cat > mail.txt << 'EOF'
# Add email addresses here, one per line
# Example: <EMAIL>

EOF
        echo -e "${GREEN}✅ mail.txt template created${NC}"
    fi
}

# Remove Vietnamese language and keep only English
remove_vietnamese() {
    echo -e "${BLUE}🌐 Removing Vietnamese language support...${NC}"
    
    # Remove Vietnamese README
    if [ -f "README-VN.md" ]; then
        rm README-VN.md
        echo -e "${GREEN}✅ Vietnamese README removed${NC}"
    fi
    
    echo -e "${GREEN}✅ Language cleanup completed${NC}"
}

# Run the bot
run_bot() {
    echo -e "${BLUE}🚀 Starting Rise Testnet Bot...${NC}"
    echo -e "${CYAN}================================================================================${NC}"
    echo -e "${YELLOW}⚠️  SECURITY REMINDERS:${NC}"
    echo -e "${YELLOW}   • Only use testnet wallets${NC}"
    echo -e "${YELLOW}   • Never use mainnet private keys${NC}"
    echo -e "${YELLOW}   • Double-check all destination addresses${NC}"
    echo -e "${YELLOW}   • Monitor all transactions on explorer${NC}"
    echo -e "${CYAN}================================================================================${NC}"
    echo ""
    
    # Activate virtual environment and run
    source venv/bin/activate
    $PYTHON_CMD main.py
}

# Main execution
main() {
    print_banner
    
    echo -e "${BLUE}🔧 Starting setup process...${NC}"
    echo ""
    
    check_python
    check_pip
    setup_venv
    install_dependencies
    setup_config
    remove_vietnamese
    
    echo ""
    echo -e "${GREEN}✅ Setup completed successfully!${NC}"
    echo -e "${CYAN}================================================================================${NC}"
    echo ""
    
    # Ask user if they want to run the bot
    echo -e "${YELLOW}Do you want to run the bot now? (y/n): ${NC}"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo ""
        run_bot
    else
        echo ""
        echo -e "${GREEN}✅ Setup complete! Run the bot anytime with:${NC}"
        echo -e "${CYAN}   ./run.sh${NC}"
        echo -e "${YELLOW}   or${NC}"
        echo -e "${CYAN}   source venv/bin/activate && python main.py${NC}"
        echo ""
    fi
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
