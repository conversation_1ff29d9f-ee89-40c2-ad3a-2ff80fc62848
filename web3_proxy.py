#!/usr/bin/env python3
"""
Web3 Proxy Integration
======================
Integrate proxy support into Web3 connections for Rise Testnet Bot

Features:
- Proxy-enabled Web3 connections
- Automatic proxy rotation per wallet
- Fallback to direct connection if proxy fails
- Session management with proxy
"""

import os
import aiohttp
import requests
from web3 import Web3
from web3.providers import HTTPProvider
from colorama import Fore, Style
from typing import Optional
from proxy_manager import proxy_manager

class ProxyHTTP<PERSON>rovider(HTTPProvider):
    """Custom HTTP Provider with proxy support"""
    
    def __init__(self, endpoint_uri, proxy_url=None, request_kwargs=None):
        self.proxy_url = proxy_url
        super().__init__(endpoint_uri, request_kwargs)
    
    def make_request(self, method, params):
        """Override make_request to use proxy"""
        if self.proxy_url:
            # Add proxy to request kwargs
            if not hasattr(self, '_request_kwargs'):
                self._request_kwargs = {}
            
            self._request_kwargs['proxies'] = {
                'http': f'http://{self.proxy_url}',
                'https': f'http://{self.proxy_url}'
            }
            self._request_kwargs['timeout'] = 30
        
        return super().make_request(method, params)

def create_web3_with_proxy(rpc_url: str, proxy: Optional[str] = None, wallet_index: int = 0) -> Web3:
    """Create Web3 instance with optional proxy"""
    
    # Check if proxy usage is enabled
    use_proxy = os.getenv('USE_PROXY', 'false').lower() == 'true'
    
    if use_proxy and proxy_manager.working_proxies:
        # Get proxy for this wallet
        if proxy is None:
            proxy = proxy_manager.get_proxy_for_wallet(wallet_index)
        
        if proxy:
            try:
                # Create Web3 with proxy
                provider = ProxyHTTPProvider(rpc_url, proxy_url=proxy)
                w3 = Web3(provider)
                
                # Test connection
                if w3.is_connected():
                    print(f"{Fore.GREEN}✅ Web3 connected via proxy: {proxy}{Style.RESET_ALL}")
                    return w3
                else:
                    print(f"{Fore.YELLOW}⚠️ Proxy connection failed, removing bad proxy: {proxy}{Style.RESET_ALL}")
                    proxy_manager.remove_bad_proxy(proxy)
                    
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ Proxy error: {str(e)[:50]}... Removing proxy: {proxy}{Style.RESET_ALL}")
                proxy_manager.remove_bad_proxy(proxy)
    
    # Fallback to direct connection
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        if w3.is_connected():
            print(f"{Fore.CYAN}🔗 Web3 connected directly (no proxy){Style.RESET_ALL}")
            return w3
        else:
            raise Exception("Direct connection failed")
    except Exception as e:
        print(f"{Fore.RED}❌ Web3 connection failed: {e}{Style.RESET_ALL}")
        raise

async def make_proxy_request(url: str, method: str = 'GET', data=None, headers=None, wallet_index: int = 0) -> Optional[requests.Response]:
    """Make HTTP request with proxy rotation"""
    
    # Check if proxy usage is enabled
    use_proxy = os.getenv('USE_PROXY', 'false').lower() == 'true'
    
    if not headers:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    if use_proxy and proxy_manager.working_proxies:
        # Get proxy for this wallet
        proxy = proxy_manager.get_proxy_for_wallet(wallet_index)
        
        if proxy:
            try:
                proxies = {
                    'http': f'http://{proxy}',
                    'https': f'http://{proxy}'
                }
                
                if method.upper() == 'GET':
                    response = requests.get(url, headers=headers, proxies=proxies, timeout=30, verify=False)
                elif method.upper() == 'POST':
                    response = requests.post(url, headers=headers, data=data, json=data if isinstance(data, dict) else None, 
                                           proxies=proxies, timeout=30, verify=False)
                else:
                    response = requests.request(method, url, headers=headers, data=data, proxies=proxies, timeout=30, verify=False)
                
                if response.status_code == 200:
                    print(f"{Fore.GREEN}✅ Request successful via proxy: {proxy}{Style.RESET_ALL}")
                    return response
                else:
                    print(f"{Fore.YELLOW}⚠️ Proxy request failed (status {response.status_code}), trying direct...{Style.RESET_ALL}")
                    
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ Proxy request error: {str(e)[:50]}... Trying direct...{Style.RESET_ALL}")
                proxy_manager.remove_bad_proxy(proxy)
    
    # Fallback to direct request
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=30, verify=False)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=headers, data=data, json=data if isinstance(data, dict) else None, timeout=30, verify=False)
        else:
            response = requests.request(method, url, headers=headers, data=data, timeout=30, verify=False)
        
        print(f"{Fore.CYAN}🔗 Request successful (direct connection){Style.RESET_ALL}")
        return response
        
    except Exception as e:
        print(f"{Fore.RED}❌ Request failed: {e}{Style.RESET_ALL}")
        return None

class ProxySession:
    """Session manager with proxy support"""
    
    def __init__(self, wallet_index: int = 0):
        self.wallet_index = wallet_index
        self.proxy = None
        self.session = None
        self._setup_session()
    
    def _setup_session(self):
        """Setup session with proxy"""
        use_proxy = os.getenv('USE_PROXY', 'false').lower() == 'true'
        
        if use_proxy and proxy_manager.working_proxies:
            self.proxy = proxy_manager.get_proxy_for_wallet(self.wallet_index)
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        if self.proxy:
            self.session.proxies = {
                'http': f'http://{self.proxy}',
                'https': f'http://{self.proxy}'
            }
            print(f"{Fore.CYAN}🔄 Session using proxy: {self.proxy}{Style.RESET_ALL}")
        else:
            print(f"{Fore.CYAN}🔗 Session using direct connection{Style.RESET_ALL}")
    
    def get(self, url: str, **kwargs):
        """GET request with proxy"""
        try:
            kwargs.setdefault('timeout', 30)
            kwargs.setdefault('verify', False)
            return self.session.get(url, **kwargs)
        except Exception as e:
            if self.proxy:
                print(f"{Fore.YELLOW}⚠️ Proxy request failed, removing proxy: {self.proxy}{Style.RESET_ALL}")
                proxy_manager.remove_bad_proxy(self.proxy)
                # Retry without proxy
                self.proxy = None
                self.session.proxies = {}
                return self.session.get(url, **kwargs)
            raise e
    
    def post(self, url: str, **kwargs):
        """POST request with proxy"""
        try:
            kwargs.setdefault('timeout', 30)
            kwargs.setdefault('verify', False)
            return self.session.post(url, **kwargs)
        except Exception as e:
            if self.proxy:
                print(f"{Fore.YELLOW}⚠️ Proxy request failed, removing proxy: {self.proxy}{Style.RESET_ALL}")
                proxy_manager.remove_bad_proxy(self.proxy)
                # Retry without proxy
                self.proxy = None
                self.session.proxies = {}
                return self.session.post(url, **kwargs)
            raise e
    
    def close(self):
        """Close session"""
        if self.session:
            self.session.close()

def get_proxy_status() -> dict:
    """Get current proxy status"""
    use_proxy = os.getenv('USE_PROXY', 'false').lower() == 'true'
    
    return {
        'enabled': use_proxy,
        'total_proxies': len(proxy_manager.working_proxies) if proxy_manager.working_proxies else 0,
        'used_proxies': len(proxy_manager.used_proxies) if proxy_manager.used_proxies else 0,
        'available_proxies': len(proxy_manager.working_proxies) - len(proxy_manager.used_proxies) if proxy_manager.working_proxies and proxy_manager.used_proxies else 0
    }

def print_proxy_status():
    """Print current proxy status"""
    status = get_proxy_status()
    
    if status['enabled']:
        print(f"{Fore.CYAN}🔄 Proxy Status:{Style.RESET_ALL}")
        print(f"   📊 Total proxies: {status['total_proxies']}")
        print(f"   🔄 Used proxies: {status['used_proxies']}")
        print(f"   ✅ Available proxies: {status['available_proxies']}")
    else:
        print(f"{Fore.YELLOW}🔗 Proxy disabled - using direct connections{Style.RESET_ALL}")
