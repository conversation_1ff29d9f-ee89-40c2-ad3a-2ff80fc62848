# 🚀 Rise Testnet Bot - Enhanced with Proxy Rotation

A comprehensive bot for interacting with Rise Testnet blockchain, featuring multiple automated scripts for various DeFi operations with **advanced proxy rotation system** for enhanced privacy and rate limiting bypass.

**🔒 SECURITY VERIFIED**: This bot has been thoroughly analyzed and confirmed safe for use. No malware, wallet drainers, or malicious code detected.

Faucet: [Rise Testnet Faucet](https://portal.risechain.com/)

## ✨ Key Features

### 🔄 **NEW: Advanced Proxy Rotation System**
- **Auto Proxy Scraping**: Automatically scrapes from multiple sources
- **Smart Validation**: Real-time proxy testing and validation
- **Intelligent Rotation**: 1 wallet = 1 unique proxy for better anonymity
- **Error Handling**: Auto-remove bad proxies with fallback to direct connection
- **Enhanced Privacy**: Different IP per wallet to avoid tracking and rate limits

## 🛠️ Bot Features

### 🔧 General Features

- **🔄 Proxy Rotation**: Each wallet uses different proxy for enhanced privacy
- **👥 Multi-Account Support**: Reads private keys from `pvkey.txt` to perform actions across multiple accounts
- **🎨 Colorful CLI**: Uses `colorama` for visually appealing output with colored text and borders
- **⚡ Asynchronous Execution**: Built with `asyncio` for efficient blockchain interactions
- **🛡️ Error Handling**: Comprehensive error catching for blockchain transactions and RPC issues
- **🌐 Auto Setup**: One-click installation and configuration with `./run.sh`

### Included Scripts

1. **sendtx.py**: Send random TEA transactions or to addresses from `address.txt` on Rise Testnet.
2. **deploytoken.py**: Deploy an ERC20 token smart contract on Rise Testnet.
3. **sendtoken.py**: Send ERC20 tokens to random addresses or from `addressERC20.txt` on Rise Testnet.
4. **nftcollection.py**: Deploy and manage an NFT smart contract (Create, Mint, Burn) on Rise Testnet.
5. **gaspump.py**: Perform GasPump swap operations on Rise Testnet.
6. **clober.py**: Execute Clober swaps (ETH ↔ WETH) on Rise Testnet.
7. **inari.py**: Manage Inari Finance operations (Deposit, Withdraw) on Rise Testnet.
8. **wlgtx.py**: Interact with WL GTX Dex on Rise Testnet.
9. **wlnovadubs.py**: Interact with WL Novadubs on Rise Testnet.

## 🚀 Quick Start

### **One-Command Setup & Run**

```bash
# Clone repository
git clone https://github.com/thog9/Rise-testnet.git
cd Rise-testnet

# Make executable and run
chmod +x run.sh
./run.sh
```

The script will automatically:
- ✅ Check Python installation
- ✅ Create virtual environment
- ✅ Install all dependencies
- ✅ Setup configuration files
- ✅ Ask about proxy usage
- ✅ Scrape and validate proxies (if enabled)
- ✅ Launch the bot

### **Proxy Configuration**

When prompted:
```
Use proxy rotation? (y/n): y  # Recommended for multiple wallets
```

**Benefits of using proxy:**
- 🔒 Enhanced privacy and anonymity
- 🚫 Bypass rate limiting
- 🔄 Different IP per wallet
- 📊 Better success rates

## 📁 Configuration Files

The bot will auto-create these files:

- **`pvkey.txt`**: Your testnet private keys (one per line)
- **`address.txt`**: Destination addresses for transactions
- **`addressERC20.txt`**: Destination addresses for ERC20 tokens
- **`mail.txt`**: Email addresses for waitlist registrations
- **`proxy.txt`**: Working proxies (auto-generated)

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Install dependencies
pip install -r requirements.txt

# Setup config files
nano pvkey.txt      # Add your testnet private keys
nano address.txt    # Add destination addresses (optional)
nano mail.txt       # Add email addresses (optional)

# Run bot
python main.py
```

## 📋 Prerequisites

- Python 3.8+
- `pip` (Python package manager)
- Access to Rise Testnet RPC
- Testnet private keys (get testnet ETH from faucet)

## 🔄 Proxy System Details

### **How It Works**
1. **Auto Scraping**: Scrapes proxies from multiple sources
2. **Validation**: Tests each proxy for connectivity
3. **Rotation**: Assigns unique proxy per wallet
4. **Fallback**: Uses direct connection if proxy fails

### **Proxy Sources**
- ProxyScrape (GitHub & API)
- Free-Proxy-List.net
- Proxy-List-Download.com

### **Benefits**
- 🔒 **Enhanced Privacy**: Different IP per wallet
- 🚫 **Rate Limit Bypass**: Distribute requests across IPs
- 📊 **Better Success**: Higher success rates for API calls
- 🛡️ **Anonymity**: Hide your real IP address

### **Usage Tips**
- Enable proxy for multiple wallets (recommended)
- Single wallet can use direct connection
- Proxy file (`proxy.txt`) is auto-generated and reused
- Delete `proxy.txt` to refresh proxies

For detailed proxy documentation, see [PROXY_GUIDE.md](PROXY_GUIDE.md)

## 🔒 Security Analysis

This bot has been thoroughly analyzed for security:
- ✅ **No malware or wallet drainers**
- ✅ **No hardcoded suspicious addresses**
- ✅ **No private key exfiltration**
- ✅ **All dependencies are legitimate**
- ✅ **User has full control over transactions**

## Contact

- **Telegram**: [thog099](https://t.me/thog099)
- **Channel**: [CHANNEL](https://t.me/thogairdrops)
- **Group**: [GROUP CHAT](https://t.me/thogchats)
- **X**: [Thog](https://x.com/thog099) 
