# 🔄 PROXY ROTATION GUIDE - RISE TESTNET BOT

## 📋 OVERVIEW

Bot Rise Testnet sekarang dilengkapi dengan **Proxy Rotation System** yang canggih untuk meningkatkan privasi dan menghindari rate limiting saat menggunakan multiple wallets.

---

## 🚀 FITUR PROXY SYSTEM

### ✅ **Auto Proxy Scraping**
- Scraping otomatis dari multiple sources
- ProxyScrape (GitHub & API)
- Free-Proxy-List.net
- Proxy-List-Download.com

### ✅ **Smart Validation**
- Validasi proxy secara real-time
- Concurrent testing untuk speed
- Auto-remove proxy yang error
- Response time monitoring

### ✅ **Intelligent Rotation**
- 1 wallet = 1 proxy unik
- Auto-rotation saat proxy error
- Fallback ke direct connection
- Session management

### ✅ **Seamless Integration**
- Web3 connections dengan proxy
- HTTP requests dengan proxy
- Automatic error handling
- Zero configuration needed

---

## 🔧 CARA PENGGUNAAN

### **1. Jalankan Bot dengan Proxy**

```bash
./run.sh
```

### **2. Pilih Proxy Option**

Saat ditanya:
```
Use proxy rotation? (y/n): y
```

### **3. Automatic Setup**

Bot akan otomatis:
- ✅ Scraping proxies dari multiple sources
- ✅ Validasi semua proxy yang ditemukan
- ✅ Save working proxies ke `proxy.txt`
- ✅ Setup proxy rotation system

### **4. Proxy Status Display**

Bot akan menampilkan:
```
🔄 Proxy Status:
   📊 Total proxies: 23
   🔄 Used proxies: 5
   ✅ Available proxies: 18
```

---

## 📊 PROXY ROTATION LOGIC

### **Per-Wallet Assignment**
```
Wallet 1 → Proxy A
Wallet 2 → Proxy B
Wallet 3 → Proxy C
...
```

### **Error Handling**
```
Proxy Error → Remove Bad Proxy → Get New Proxy → Continue
```

### **Fallback System**
```
Proxy Failed → Direct Connection → Continue Operation
```

---

## 🔍 TECHNICAL DETAILS

### **Supported Operations**

1. **Web3 Connections**
   - ETH transactions via proxy
   - Smart contract interactions
   - Balance checking

2. **HTTP Requests**
   - Waitlist registrations
   - API calls
   - Data fetching

3. **Session Management**
   - Persistent proxy sessions
   - Auto-retry on failure
   - Connection pooling

### **Proxy Sources**

| Source | Type | Reliability |
|--------|------|-------------|
| ProxyScrape GitHub | HTTP/HTTPS | ⭐⭐⭐⭐⭐ |
| ProxyScrape API | HTTP/SOCKS | ⭐⭐⭐⭐ |
| Free-Proxy-List | HTTP/HTTPS | ⭐⭐⭐ |
| Proxy-List-Download | Mixed | ⭐⭐⭐ |

---

## 📁 FILE STRUCTURE

```
Rise-testnet/
├── proxy_manager.py      # Core proxy management
├── web3_proxy.py         # Web3 & HTTP proxy integration
├── proxy.txt             # Working proxies (auto-generated)
├── run.sh                # Enhanced with proxy options
└── scripts/
    ├── clober.py         # Updated with proxy support
    ├── wlgtx.py          # Updated with proxy support
    └── ...               # Other scripts
```

---

## ⚙️ CONFIGURATION

### **Environment Variables**

Bot otomatis set environment variable:
```bash
export USE_PROXY=true   # Enable proxy
export USE_PROXY=false  # Disable proxy
```

### **Proxy File Format**

`proxy.txt` format:
```
***********:8080
********:3128
***********:8888
...
```

---

## 🛠️ TROUBLESHOOTING

### **Common Issues**

#### **1. No Working Proxies Found**
```
❌ No working proxies found
```
**Solution:**
- Check internet connection
- Try running again (proxy sources change)
- Use bot without proxy (select 'n')

#### **2. Proxy Connection Timeout**
```
⚠️ Proxy request error: timeout
```
**Solution:**
- Bot automatically removes bad proxy
- Continues with next available proxy
- Falls back to direct connection

#### **3. Rate Limiting**
```
⚠️ Proxy request failed (status 429)
```
**Solution:**
- Bot rotates to different proxy
- Implements delay between requests
- Uses different IP for each wallet

### **Manual Proxy Management**

#### **Check Proxy Status**
```python
from web3_proxy import get_proxy_status
status = get_proxy_status()
print(status)
```

#### **Force Proxy Refresh**
```bash
rm proxy.txt
./run.sh  # Will scrape fresh proxies
```

---

## 🔒 SECURITY BENEFITS

### **Enhanced Privacy**
- ✅ Different IP per wallet
- ✅ Harder to correlate transactions
- ✅ Bypass geo-restrictions
- ✅ Avoid IP-based tracking

### **Rate Limiting Bypass**
- ✅ Distribute requests across IPs
- ✅ Avoid API rate limits
- ✅ Parallel processing
- ✅ Better success rates

### **Anonymity**
- ✅ Hide real IP address
- ✅ Multiple exit points
- ✅ Traffic distribution
- ✅ Enhanced OPSEC

---

## 📈 PERFORMANCE METRICS

### **Typical Results**
- 🔍 **Proxy Discovery**: 200-500 proxies found
- ✅ **Success Rate**: 5-15% working proxies
- ⚡ **Speed**: 20-30 concurrent validations
- 💾 **Storage**: Working proxies saved locally

### **Validation Process**
```
Found 445 unique proxies
🔄 Validating 445 proxies...
✅ Found 23 working proxies
💾 Saved 23 proxies to proxy.txt
```

---

## 🎯 BEST PRACTICES

### **1. Regular Proxy Refresh**
- Delete `proxy.txt` weekly
- Let bot scrape fresh proxies
- Better success rates

### **2. Monitor Proxy Status**
- Check proxy count in status display
- Watch for error messages
- Restart if too many failures

### **3. Backup Strategy**
- Keep `proxy.txt` backup
- Test without proxy first
- Have fallback plan

### **4. Performance Optimization**
- Use proxy for multiple wallets
- Single wallet = direct connection OK
- Balance speed vs anonymity

---

## 🚀 ADVANCED USAGE

### **Custom Proxy Integration**

Add your own proxies to `proxy.txt`:
```
your-proxy-1.com:8080
your-proxy-2.com:3128
```

### **Proxy Rotation Monitoring**

Watch proxy usage in real-time:
```
🔄 Wallet 1 using proxy: ***********:8080
🔄 Wallet 2 using proxy: ********:3128
⚠️ Removed bad proxy: ***********:8888
```

---

## 📞 SUPPORT

Jika mengalami masalah dengan proxy system:

1. **Check Logs**: Perhatikan error messages
2. **Restart Bot**: `./run.sh` dan pilih proxy lagi
3. **Manual Mode**: Pilih 'n' untuk disable proxy
4. **Contact**: Telegram @thog099

---

**Happy Airdrop Hunting with Enhanced Privacy! 🚀🔒**
