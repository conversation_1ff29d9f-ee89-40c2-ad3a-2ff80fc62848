#!/usr/bin/env python3
"""
Proxy Manager for Rise Testnet Bot
==================================
Scraping, validating, and rotating proxies for each wallet

Features:
- Multi-source proxy scraping
- Automatic validation
- Proxy rotation per wallet
- Error handling and fallback
"""

import asyncio
import aiohttp
import requests
import re
import random
import time
from typing import List, Optional, Tuple
from colorama import Fore, Style
import urllib3

# Disable warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ProxyManager:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.timeout = 8
        self.working_proxies = []
        self.used_proxies = set()
        
    def print_status(self, message: str, color=Fore.CYAN):
        """Print colored status message"""
        print(f"{color}🔄 {message}{Style.RESET_ALL}")
    
    async def scrape_proxyscrape(self) -> List[str]:
        """Scrape from proxyscrape sources"""
        self.print_status("Scraping ProxyScrape sources...")
        
        urls = [
            'https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/http.txt',
            'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&format=textplain',
        ]
        
        proxies = []
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=20)) as session:
            for url in urls:
                try:
                    async with session.get(url, headers=self.headers) as response:
                        if response.status == 200:
                            content = await response.text()
                            lines = content.strip().split('\n')
                            for line in lines:
                                line = line.strip()
                                if self.is_valid_proxy_format(line):
                                    proxies.append(line)
                except Exception:
                    continue
        
        return list(set(proxies))  # Remove duplicates
    
    def scrape_free_proxy_list(self) -> List[str]:
        """Scrape from free-proxy-list.net"""
        self.print_status("Scraping Free-Proxy-List...")
        
        urls = [
            'https://free-proxy-list.net/',
            'https://www.sslproxies.org/'
        ]
        
        proxies = []
        
        for url in urls:
            try:
                response = requests.get(url, headers=self.headers, timeout=15, verify=False)
                if response.status_code == 200:
                    # Extract IP:PORT patterns
                    pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})'
                    matches = re.findall(pattern, response.text)
                    
                    for ip, port in matches:
                        proxy = f"{ip}:{port}"
                        if self.is_valid_proxy_format(proxy):
                            proxies.append(proxy)
            except Exception:
                continue
        
        return list(set(proxies))
    
    def is_valid_proxy_format(self, proxy: str) -> bool:
        """Validate proxy format"""
        if not proxy or ':' not in proxy:
            return False
        
        try:
            # Remove protocol if present
            if '://' in proxy:
                proxy = proxy.split('://', 1)[1]
            
            parts = proxy.split(':')
            if len(parts) != 2:
                return False
            
            ip, port = parts
            
            # Validate IP
            ip_parts = ip.split('.')
            if len(ip_parts) != 4:
                return False
            
            for part in ip_parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    return False
            
            # Validate port
            if not port.isdigit() or not (1 <= int(port) <= 65535):
                return False
            
            return True
        except:
            return False
    
    async def validate_proxy(self, proxy: str) -> bool:
        """Validate single proxy quickly"""
        test_urls = [
            'http://httpbin.org/ip',
            'https://api.ipify.org?format=json'
        ]
        
        try:
            proxy_url = f"http://{proxy}" if '://' not in proxy else proxy
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                for test_url in test_urls:
                    try:
                        async with session.get(test_url, proxy=proxy_url) as response:
                            if response.status == 200:
                                await response.text()
                                return True
                    except:
                        continue
            
            return False
        except:
            return False
    
    async def validate_proxies_batch(self, proxies: List[str], max_concurrent: int = 30) -> List[str]:
        """Validate proxies in batches"""
        if not proxies:
            return []
        
        self.print_status(f"Validating {len(proxies)} proxies...")
        
        # Limit to reasonable number for speed
        sample_size = min(100, len(proxies))
        sample_proxies = random.sample(proxies, sample_size)
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def validate_single(proxy):
            async with semaphore:
                is_working = await self.validate_proxy(proxy)
                if is_working:
                    return proxy
                return None
        
        tasks = [validate_single(proxy) for proxy in sample_proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        working = [proxy for proxy in results if proxy and isinstance(proxy, str)]
        
        print(f"{Fore.GREEN}✅ Found {len(working)} working proxies{Style.RESET_ALL}")
        return working
    
    async def scrape_and_validate(self) -> List[str]:
        """Scrape from all sources and validate"""
        self.print_status("Starting proxy scraping...")
        
        # Scrape from multiple sources
        tasks = [
            self.scrape_proxyscrape(),
            asyncio.to_thread(self.scrape_free_proxy_list)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        all_proxies = []
        for result in results:
            if isinstance(result, list):
                all_proxies.extend(result)
        
        # Remove duplicates
        unique_proxies = list(set(all_proxies))
        print(f"{Fore.YELLOW}📊 Found {len(unique_proxies)} unique proxies{Style.RESET_ALL}")
        
        if not unique_proxies:
            print(f"{Fore.RED}❌ No proxies found from any source{Style.RESET_ALL}")
            return []
        
        # Validate proxies
        working_proxies = await self.validate_proxies_batch(unique_proxies)
        
        if working_proxies:
            self.working_proxies = working_proxies
            self.save_to_file(working_proxies)
            return working_proxies
        else:
            print(f"{Fore.RED}❌ No working proxies found{Style.RESET_ALL}")
            return []
    
    def save_to_file(self, proxies: List[str], filename: str = 'proxy.txt'):
        """Save proxies to file"""
        try:
            with open(filename, 'w') as f:
                for proxy in proxies:
                    f.write(f"{proxy}\n")
            print(f"{Fore.GREEN}💾 Saved {len(proxies)} proxies to {filename}{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}❌ Error saving proxies: {e}{Style.RESET_ALL}")
    
    def load_from_file(self, filename: str = 'proxy.txt') -> List[str]:
        """Load proxies from file"""
        try:
            with open(filename, 'r') as f:
                proxies = [line.strip() for line in f if line.strip()]
            
            # Validate format
            valid_proxies = [p for p in proxies if self.is_valid_proxy_format(p)]
            
            if valid_proxies:
                self.working_proxies = valid_proxies
                print(f"{Fore.GREEN}📂 Loaded {len(valid_proxies)} proxies from {filename}{Style.RESET_ALL}")
                return valid_proxies
            else:
                print(f"{Fore.YELLOW}⚠️ No valid proxies in {filename}{Style.RESET_ALL}")
                return []
        except FileNotFoundError:
            print(f"{Fore.YELLOW}⚠️ {filename} not found{Style.RESET_ALL}")
            return []
        except Exception as e:
            print(f"{Fore.RED}❌ Error loading proxies: {e}{Style.RESET_ALL}")
            return []
    
    def get_proxy_for_wallet(self, wallet_index: int) -> Optional[str]:
        """Get unique proxy for wallet with rotation"""
        if not self.working_proxies:
            return None
        
        # Try to get unused proxy first
        available_proxies = [p for p in self.working_proxies if p not in self.used_proxies]
        
        if not available_proxies:
            # If all proxies used, reset and start over
            self.used_proxies.clear()
            available_proxies = self.working_proxies.copy()
        
        if available_proxies:
            # Select proxy based on wallet index for consistency
            proxy_index = wallet_index % len(available_proxies)
            selected_proxy = available_proxies[proxy_index]
            self.used_proxies.add(selected_proxy)
            
            print(f"{Fore.CYAN}🔄 Wallet {wallet_index + 1} using proxy: {selected_proxy}{Style.RESET_ALL}")
            return selected_proxy
        
        return None
    
    def remove_bad_proxy(self, proxy: str):
        """Remove proxy that failed"""
        if proxy in self.working_proxies:
            self.working_proxies.remove(proxy)
            self.used_proxies.discard(proxy)
            print(f"{Fore.YELLOW}⚠️ Removed bad proxy: {proxy}{Style.RESET_ALL}")
    
    async def setup_proxies(self) -> bool:
        """Setup proxies - load from file or scrape new ones"""
        print(f"{Fore.CYAN}🔄 Setting up proxy system...{Style.RESET_ALL}")
        
        # Try to load existing proxies first
        existing_proxies = self.load_from_file()
        
        if existing_proxies and len(existing_proxies) >= 5:
            # Quick validation of existing proxies
            print(f"{Fore.YELLOW}🔍 Quick validation of existing proxies...{Style.RESET_ALL}")
            sample_proxies = random.sample(existing_proxies, min(5, len(existing_proxies)))
            working_sample = await self.validate_proxies_batch(sample_proxies, max_concurrent=10)
            
            if len(working_sample) >= 2:  # At least 2 working proxies
                print(f"{Fore.GREEN}✅ Using existing proxies ({len(existing_proxies)} total){Style.RESET_ALL}")
                return True
        
        # Scrape new proxies
        print(f"{Fore.YELLOW}🔄 Scraping fresh proxies...{Style.RESET_ALL}")
        new_proxies = await self.scrape_and_validate()
        
        if new_proxies and len(new_proxies) >= 2:
            print(f"{Fore.GREEN}✅ Proxy setup complete ({len(new_proxies)} working proxies){Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}❌ Failed to setup proxies{Style.RESET_ALL}")
            return False

# Global proxy manager instance
proxy_manager = ProxyManager()
